import React, { useRef, useEffect, useState } from "react";
import { useCurrentFrame, interpolate } from "remotion";
import { gsap } from "gsap";
import { SplitText as GSAPSplitText } from "gsap/SplitText";

gsap.registerPlugin(GSAPSplitText);

// 缓动函数类型
type EasingFunction = (t: number) => number;

// 预定义的缓动函数
export const easingFunctions = {
  linear: (t: number) => t,
  easeOut: (t: number) => 1 - Math.pow(1 - t, 3),
  easeIn: (t: number) => t * t * t,
  easeInOut: (t: number) => t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2,
  elastic: (t: number) => {
    const c4 = (2 * Math.PI) / 3;
    return t === 0 ? 0 : t === 1 ? 1 : -Math.pow(2, 10 * t - 10) * Math.sin((t * 10 - 10.75) * c4);
  },
  elasticOut: (t: number) => {
    const c4 = (2 * Math.PI) / 3;
    return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1;
  },
  bounce: (t: number) => {
    const n1 = 7.5625;
    const d1 = 2.75;
    if (t < 1 / d1) {
      return n1 * t * t;
    } else if (t < 2 / d1) {
      return n1 * (t -= 1.5 / d1) * t + 0.75;
    } else if (t < 2.5 / d1) {
      return n1 * (t -= 2.25 / d1) * t + 0.9375;
    } else {
      return n1 * (t -= 2.625 / d1) * t + 0.984375;
    }
  },
  back: (t: number) => {
    const c1 = 1.70158;
    const c3 = c1 + 1;
    return c3 * t * t * t - c1 * t * t;
  },
  backOut: (t: number) => {
    const c1 = 1.70158;
    const c3 = c1 + 1;
    return 1 + c3 * Math.pow(t - 1, 3) + c1 * Math.pow(t - 1, 2);
  },
} as const;

export interface SplitTextProps {
  text: string;
  className?: string;
  delay?: number;
  startFrame?: number;
  endFrame?: number;
  duration?: number; // 总动画持续时间（帧数），会自动计算 delay
  charDuration?: number; // 单个字符动画持续时间（帧数），默认15帧
  autoAdjustSpeed?: boolean; // 是否根据持续时间自动调整速度
  easing?: keyof typeof easingFunctions | EasingFunction; // 缓动函数
  splitType?: "chars" | "words" | "lines" | "words, chars";
  from?: {
    opacity?: number;
    y?: number;
    x?: number;
    scale?: number;
    rotation?: number;
  };
  to?: {
    opacity?: number;
    y?: number;
    x?: number;
    scale?: number;
    rotation?: number;
  };
  tag?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "p" | "span";
  textAlign?: React.CSSProperties["textAlign"];
  onAnimationComplete?: () => void;
}

const SplitText: React.FC<SplitTextProps> = ({
  text,
  className = "",
  delay,
  startFrame = 0,
  endFrame,
  duration,
  charDuration = 20,
  autoAdjustSpeed = true,
  easing = "elasticOut",
  splitType = "chars",
  from = { opacity: 0, y: 40 },
  to = { opacity: 1, y: 0 },
  tag = "p",
  textAlign = "center",
  onAnimationComplete,
}) => {
  const frame = useCurrentFrame();
  const ref = useRef<HTMLParagraphElement>(null);
  const [splitInstance, setSplitInstance] = useState<GSAPSplitText | null>(null);
  const [targets, setTargets] = useState<Element[]>([]);
  const [fontsLoaded, setFontsLoaded] = useState<boolean>(false);

  // 计算实际的动画参数
  const getAnimationParams = () => {
    const textLength = text.length;

    // 如果提供了 duration，自动计算其他参数
    if (duration && autoAdjustSpeed) {
      const calculatedEndFrame = startFrame + duration;
      const calculatedDelay = Math.max(1, Math.floor(duration / (textLength * 1.5))); // 确保有足够的重叠

      return {
        actualStartFrame: startFrame,
        actualEndFrame: calculatedEndFrame,
        actualDelay: calculatedDelay,
      };
    }

    // 如果提供了 endFrame 和 delay，直接使用
    if (endFrame !== undefined && delay !== undefined) {
      return {
        actualStartFrame: startFrame,
        actualEndFrame: endFrame,
        actualDelay: delay,
      };
    }

    // 如果只提供了 endFrame，自动计算 delay
    if (endFrame !== undefined) {
      const totalDuration = endFrame - startFrame;
      const calculatedDelay = Math.max(1, Math.floor(totalDuration / (textLength * 1.5)));

      return {
        actualStartFrame: startFrame,
        actualEndFrame: endFrame,
        actualDelay: calculatedDelay,
      };
    }

    // 默认值
    const defaultDuration = 60;
    const defaultDelay = Math.max(1, Math.floor(defaultDuration / (textLength * 1.5)));

    return {
      actualStartFrame: startFrame,
      actualEndFrame: startFrame + defaultDuration,
      actualDelay: defaultDelay,
    };
  };

  const { actualStartFrame, actualEndFrame, actualDelay } = getAnimationParams();

  // Initialize fonts loading
  useEffect(() => {
    if (document.fonts.status === "loaded") {
      setFontsLoaded(true);
    } else {
      document.fonts.ready.then(() => {
        setFontsLoaded(true);
      });
    }
  }, []);

  // Initialize SplitText when fonts are loaded
  useEffect(() => {
    if (!ref.current || !text || !fontsLoaded) return;

    const el = ref.current;
    const split = new GSAPSplitText(el, {
      type: splitType,
      smartWrap: true,
      autoSplit: splitType === "lines",
      linesClass: "split-line",
      wordsClass: "split-word",
      charsClass: "split-char",
      reduceWhiteSpace: false,
    });

    setSplitInstance(split);

    // Determine targets based on split type
    let newTargets: Element[] = [];
    if (splitType.includes("chars") && split.chars?.length) {
      newTargets = split.chars;
    } else if (splitType.includes("words") && split.words?.length) {
      newTargets = split.words;
    } else if (splitType.includes("lines") && split.lines?.length) {
      newTargets = split.lines;
    }

    setTargets(newTargets);

    return () => {
      try {
        split.revert();
      } catch (_) {}
    };
  }, [text, splitType, fontsLoaded]);

  // Apply frame-based animations
  useEffect(() => {
    if (!targets.length) return;

    // 获取缓动函数
    const easingFn = typeof easing === 'string' ? easingFunctions[easing] : easing;

    targets.forEach((target, index) => {
      const elementStartFrame = actualStartFrame + (index * actualDelay);
      // 每个字符的动画持续时间
      const elementEndFrame = elementStartFrame + charDuration;

      // Calculate linear progress for this element
      const linearProgress = interpolate(
        frame,
        [elementStartFrame, elementEndFrame],
        [0, 1],
        {
          extrapolateLeft: "clamp",
          extrapolateRight: "clamp",
        }
      );

      // Apply easing function to get eased progress
      const easedProgress = easingFn(linearProgress);

      // Apply interpolated styles with eased progress
      const opacity = interpolate(easedProgress, [0, 1], [from.opacity ?? 0, to.opacity ?? 1]);
      const y = interpolate(easedProgress, [0, 1], [from.y ?? 0, to.y ?? 0]);
      const x = interpolate(easedProgress, [0, 1], [from.x ?? 0, to.x ?? 0]);
      const scale = interpolate(easedProgress, [0, 1], [from.scale ?? 1, to.scale ?? 1]);
      const rotation = interpolate(easedProgress, [0, 1], [from.rotation ?? 0, to.rotation ?? 0]);

      const element = target as HTMLElement;
      element.style.opacity = opacity.toString();
      element.style.transform = `translate3d(${x}px, ${y}px, 0) scale(${scale}) rotate(${rotation}deg)`;
      element.style.willChange = "transform, opacity";
    });

    // Check if animation is complete
    const lastElementEndFrame = actualStartFrame + ((targets.length - 1) * actualDelay) + charDuration;
    if (frame >= lastElementEndFrame && onAnimationComplete) {
      onAnimationComplete();
    }
  }, [frame, targets, actualStartFrame, actualEndFrame, actualDelay, charDuration, easing, from, to, onAnimationComplete]);

  const renderTag = () => {
    const style: React.CSSProperties = {
      textAlign,
      wordWrap: "break-word",
      willChange: "transform, opacity",
    };
    const classes = `split-parent overflow-hidden inline-block whitespace-normal ${className}`;
    switch (tag) {
      case "h1":
        return (
          <h1 ref={ref} style={style} className={classes}>
            {text}
          </h1>
        );
      case "h2":
        return (
          <h2 ref={ref} style={style} className={classes}>
            {text}
          </h2>
        );
      case "h3":
        return (
          <h3 ref={ref} style={style} className={classes}>
            {text}
          </h3>
        );
      case "h4":
        return (
          <h4 ref={ref} style={style} className={classes}>
            {text}
          </h4>
        );
      case "h5":
        return (
          <h5 ref={ref} style={style} className={classes}>
            {text}
          </h5>
        );
      case "h6":
        return (
          <h6 ref={ref} style={style} className={classes}>
            {text}
          </h6>
        );
      default:
        return (
          <p ref={ref} style={style} className={classes}>
            {text}
          </p>
        );
    }
  };

  return renderTag();
};

export default SplitText;
