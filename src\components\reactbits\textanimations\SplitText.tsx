import React, { useRef, useEffect, useState } from "react";
import { useCurrentFrame, interpolate } from "remotion";
import { gsap } from "gsap";
import { SplitText as GSAPSplitText } from "gsap/SplitText";

gsap.registerPlugin(GSAPSplitText);

export interface SplitTextProps {
  text: string;
  className?: string;
  delay?: number;
  startFrame?: number;
  endFrame?: number;
  splitType?: "chars" | "words" | "lines" | "words, chars";
  from?: {
    opacity?: number;
    y?: number;
    x?: number;
    scale?: number;
    rotation?: number;
  };
  to?: {
    opacity?: number;
    y?: number;
    x?: number;
    scale?: number;
    rotation?: number;
  };
  tag?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "p" | "span";
  textAlign?: React.CSSProperties["textAlign"];
  onAnimationComplete?: () => void;
}

const SplitText: React.FC<SplitTextProps> = ({
  text,
  className = "",
  delay = 5, // frames between each character animation
  startFrame = 0,
  endFrame = 60,
  splitType = "chars",
  from = { opacity: 0, y: 40 },
  to = { opacity: 1, y: 0 },
  tag = "p",
  textAlign = "center",
  onAnimationComplete,
}) => {
  const frame = useCurrentFrame();
  const ref = useRef<HTMLParagraphElement>(null);
  const [splitInstance, setSplitInstance] = useState<GSAPSplitText | null>(null);
  const [targets, setTargets] = useState<Element[]>([]);
  const [fontsLoaded, setFontsLoaded] = useState<boolean>(false);

  // Initialize fonts loading
  useEffect(() => {
    if (document.fonts.status === "loaded") {
      setFontsLoaded(true);
    } else {
      document.fonts.ready.then(() => {
        setFontsLoaded(true);
      });
    }
  }, []);

  // Initialize SplitText when fonts are loaded
  useEffect(() => {
    if (!ref.current || !text || !fontsLoaded) return;

    const el = ref.current;
    const split = new GSAPSplitText(el, {
      type: splitType,
      smartWrap: true,
      autoSplit: splitType === "lines",
      linesClass: "split-line",
      wordsClass: "split-word",
      charsClass: "split-char",
      reduceWhiteSpace: false,
    });

    setSplitInstance(split);

    // Determine targets based on split type
    let newTargets: Element[] = [];
    if (splitType.includes("chars") && split.chars?.length) {
      newTargets = split.chars;
    } else if (splitType.includes("words") && split.words?.length) {
      newTargets = split.words;
    } else if (splitType.includes("lines") && split.lines?.length) {
      newTargets = split.lines;
    }

    setTargets(newTargets);

    return () => {
      try {
        split.revert();
      } catch (_) {}
    };
  }, [text, splitType, fontsLoaded]);

  // Apply frame-based animations
  useEffect(() => {
    if (!targets.length) return;

    targets.forEach((target, index) => {
      const elementStartFrame = startFrame + (index * delay);
      const elementEndFrame = elementStartFrame + (endFrame - startFrame);

      // Calculate progress for this element
      const progress = interpolate(
        frame,
        [elementStartFrame, elementEndFrame],
        [0, 1],
        {
          extrapolateLeft: "clamp",
          extrapolateRight: "clamp",
        }
      );

      // Apply interpolated styles
      const opacity = interpolate(progress, [0, 1], [from.opacity ?? 0, to.opacity ?? 1]);
      const y = interpolate(progress, [0, 1], [from.y ?? 0, to.y ?? 0]);
      const x = interpolate(progress, [0, 1], [from.x ?? 0, to.x ?? 0]);
      const scale = interpolate(progress, [0, 1], [from.scale ?? 1, to.scale ?? 1]);
      const rotation = interpolate(progress, [0, 1], [from.rotation ?? 0, to.rotation ?? 0]);

      const element = target as HTMLElement;
      element.style.opacity = opacity.toString();
      element.style.transform = `translate3d(${x}px, ${y}px, 0) scale(${scale}) rotate(${rotation}deg)`;
      element.style.willChange = "transform, opacity";
    });

    // Check if animation is complete
    const lastElementEndFrame = startFrame + ((targets.length - 1) * delay) + (endFrame - startFrame);
    if (frame >= lastElementEndFrame && onAnimationComplete) {
      onAnimationComplete();
    }
  }, [frame, targets, startFrame, endFrame, delay, from, to, onAnimationComplete]);

  const renderTag = () => {
    const style: React.CSSProperties = {
      textAlign,
      wordWrap: "break-word",
      willChange: "transform, opacity",
    };
    const classes = `split-parent overflow-hidden inline-block whitespace-normal ${className}`;
    switch (tag) {
      case "h1":
        return (
          <h1 ref={ref} style={style} className={classes}>
            {text}
          </h1>
        );
      case "h2":
        return (
          <h2 ref={ref} style={style} className={classes}>
            {text}
          </h2>
        );
      case "h3":
        return (
          <h3 ref={ref} style={style} className={classes}>
            {text}
          </h3>
        );
      case "h4":
        return (
          <h4 ref={ref} style={style} className={classes}>
            {text}
          </h4>
        );
      case "h5":
        return (
          <h5 ref={ref} style={style} className={classes}>
            {text}
          </h5>
        );
      case "h6":
        return (
          <h6 ref={ref} style={style} className={classes}>
            {text}
          </h6>
        );
      default:
        return (
          <p ref={ref} style={style} className={classes}>
            {text}
          </p>
        );
    }
  };

  return renderTag();
};

export default SplitText;
