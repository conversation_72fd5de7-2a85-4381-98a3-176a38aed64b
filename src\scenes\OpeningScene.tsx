import React from 'react';
import { AbsoluteFill, useCurrentFrame } from 'remotion';
import { SceneProps } from '../types';
import SplitText from '../components/reactbits/textanimations/SplitText';
import Aurora from '../components/reactbits/backgrounds/Aurora';

const OpeningScene: React.FC<SceneProps> = ({ frame, durationInFrames }) => {
  const currentFrame = useCurrentFrame();

  return (
    <AbsoluteFill className="bg-black flex flex-col items-center justify-center text-white relative overflow-hidden">
      {/* Aurora 背景 */}
      <div className="absolute inset-0 opacity-60">
        <Aurora
          colorStops={["#5227FF", "#7cff67", "#ff6b6b", "#4ecdc4"]}
          amplitude={1.2}
          blend={0.7}
          speed={0.8}
        />
      </div>

      {/* 主标题区域 */}
      <div className="relative z-10 text-center max-w-4xl px-8">
        {/* 主标题 - 使用弹性动画 */}
        <SplitText
          text="早睡是伪命题吗？"
          className="text-9xl font-semibold text-center"
          startFrame={0}
          duration={60}
          charDuration={25} // 增加单个字符动画时长以展现弹性效果
          easing="elasticOut" // 弹性出场效果
          splitType="chars"
          from={{ opacity: 0, y: 40, scale: 0.8 }}
          to={{ opacity: 1, y: 0, scale: 1 }}
          textAlign="center"
        />

        {/* 副标题 - 使用回弹动画 */}
        <SplitText
          text="很多人相信：只要早睡，就能变健康、变聪明"
          className="text-6xl font-medium mt-8 text-center"
          startFrame={60}
          duration={60}
          charDuration={20}
          easing="backOut" // 回弹效果
          splitType="chars"
          from={{ opacity: 0, y: 30, scale: 0.9 }}
          to={{ opacity: 1, y: 0, scale: 1 }}
          textAlign="center"
        />

      </div>
    </AbsoluteFill>
  );
};

export default OpeningScene;